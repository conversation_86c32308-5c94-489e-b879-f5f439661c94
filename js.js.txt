<script>
// Wilaya and Commune Selection Script
document.addEventListener('DOMContentLoaded', function() {
    // Wilaya and Commune data structure
    const wilayaData = {
        'Adrar': ['<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>ghmir', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 'Timekt<PERSON>', 'Tit', 'T<PERSON>bit', '<PERSON><PERSON><PERSON><PERSON>'],
        'Chlef': ['<PERSON><PERSON><PERSON>', '<PERSON> Me<PERSON>', 'Benair<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>le<PERSON>', '<PERSON><PERSON>', 'El Hadjadj', 'El Karimia', '<PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Labiod Medjadja', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON> F<PERSON>da', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>'],
        '<PERSON><PERSON>': ['<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON> <PERSON><PERSON> <PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON> <PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON> <PERSON><PERSON>', '<PERSON> <PERSON><PERSON>', 'Hassi Delaa', 'Hassi Rmel', 'Kheneg', 'Ksar El Hirane', 'Laghouat', 'Oued Mzi', 'Oued Morra', 'Sebgag', 'Sidi Bouzid', 'Sidi Makhlouf', 'Tadjemout', 'Tadjrouna', 'Taouiala'],
        'Oum El Bouaghi': ['Ain Babouche', 'Ain Beida', 'Ain Diss', 'Ain Fekroune', 'Ain Kercha', 'Ain Mlila', 'Ain Zitoun', 'Behir Chergui', 'Berriche', 'Bir Chouhada', 'Dhala', 'El Amiria', 'El Belala', 'El Djazia', 'El Fedjoudj Boughrara Sa', 'El Harmilia', 'Fkirina', 'Hanchir Toumghani', 'Ksar Sbahi', 'Meskiana', 'Oued Nini', 'Ouled Gacem', 'Ouled Hamla', 'Ouled Zouai', 'Oum El Bouaghi', 'Rahia', 'Sigus', 'Souk Naamane', 'Zorg'],
        'Batna': ['Ain Djasser', 'Ain Touta', 'Ain Yagout', 'Arris', 'Azil Abedelkader', 'Barika', 'Batna', 'Beni Foudhala El Hakania', 'Bitam', 'Boulhilat', 'Boumagueur', 'Boumia', 'Bouzina', 'Chemora', 'Chir', 'Djerma', 'Djezzar', 'El Hassi', 'El Madher', 'Fesdis', 'Foum Toub', 'Ghassira', 'Gosbat', 'Guigba', 'Hidoussa', 'Ichmoul', 'Inoughissen', 'Kimmel', 'Ksar Bellezma', 'Larbaa', 'Lazrou', 'Lemsane', 'M Doukal', 'Maafa', 'Menaa', 'Merouana', 'N Gaous', 'Oued Chaaba', 'Oued El Ma', 'Oued Taga', 'Ouled Ammar', 'Ouled Aouf', 'Ouled Fadel', 'Ouled Sellem', 'Ouled Si Slimane', 'Ouyoun El Assafir', 'Rahbat', 'Ras El Aioun', 'Sefiane', 'Seggana', 'Seriana', 'T Kout', 'Talkhamt', 'Taxlent', 'Tazoult', 'Teniet El Abed', 'Tighanimine', 'Tigharghar', 'Tilatou', 'Timgad', 'Zanet El Beida'],
        'Béjaïa': ['Adekar', 'Ait Rzine', 'Ait Smail', 'Akbou', 'Akfadou', 'Amalou', 'Amizour', 'Aokas', 'Barbacha', 'Bejaia', 'Beni Dejllil', 'Beni Ksila', 'Beni Mallikeche', 'Benimaouche', 'Boudjellil', 'Bouhamza', 'Boukhelifa', 'Chellata', 'Chemini', 'Darghina', 'Dra El Caid', 'El Kseur', 'Fenaia Il Maten', 'Feraoun', 'Ighil Ali', 'Ighram', 'Kendira', 'Kherrata', 'Leflaye', 'Mcisna', 'Melbou', 'Oued Ghir', 'Ouzellaguene', 'Seddouk', 'Sidi Aich', 'Sidi Ayad', 'Smaoun', 'Souk El Tenine', 'Souk Oufella', 'Tala Hamza', 'Tamokra', 'Tamridjet', 'Taourit Ighil', 'Taskriout', 'Tazmalt', 'Tibane', 'Tichy', 'Tifra', 'Timezrit', 'Tinebdar', 'Tizi Nberber', 'Toudja'],
        'Biskra': ['Ain Naga', 'Ain Zaatout', 'Biskra', 'Bordj Ben Azzouz', 'Bouchagroun', 'Branis', 'Chetma', 'Djemorah', 'El Feidh', 'El Ghrous', 'El Hadjab', 'El Haouch', 'El Kantara', 'El Outaya', 'Foughala', 'Khenguet Sidi Nadji', 'Lichana', 'Lioua', 'Mchouneche', 'Mlili', 'Mekhadma', 'Meziraa', 'Oumache', 'Ourlal', 'Sidi Okba', 'Tolga', 'Zeribet El Oued'],
        'Béchar': ['Abadla', 'Bechar', 'Beni Ounif', 'Boukais', 'Erg Ferradj', 'Kenadsa', 'Lahmar', 'Mechraa H.boumediene', 'Meridja', 'Mogheul', 'Taghit'],
        'Blida': ['Ain Romana', 'Beni Mered', 'Beni Tamou', 'Benkhelil', 'Blida', 'Bouarfa', 'Boufarik', 'Bougara', 'Bouinan', 'Chebli', 'Chiffa', 'Chrea', 'Djebabra', 'El Affroun', 'Guerrouaou', 'Hammam Melouane', 'Larbaa', 'Meftah', 'Mouzaia', 'Oued Djer', 'Oued El Alleug', 'Ouled Slama', 'Ouled Yaich', 'Souhane', 'Souma'],
        'Bouira': ['Aghbalou', 'Ahl El Ksar', 'Ain Bessem', 'Ain El Hadjar', 'Ain Laloui', 'Ain Turk', 'Ait Laaziz', 'Aomar', 'Bechloul', 'Bir Ghbalou', 'Bordj Okhriss', 'Bouderbala', 'Bouira', 'Boukram', 'Chorfa', 'Dechmia', 'Dirah', 'Djebahia', 'El Adjiba', 'El Asnam', 'El Hachimia', 'El Hakimia', 'El Khabouzia', 'El Mokrani', 'Guerrouma', 'Hadjera Zerga', 'Haizer', 'Hanif', 'Kadiria', 'Lakhdaria', 'M Chedallah', 'Maala', 'Mamora', 'Mezdour', 'Oued El Berdi', 'Ouled Rached', 'Raouraoua', 'Ridane', 'Saharidj', 'Souk El Khemis', 'Sour El Ghozlane', 'Taghzout', 'Taguedite', 'Taourirt', 'Zbarbar'],
        'Tamanrasset': ['Abalessa', 'Ain Amguel', 'Idles', 'Tamanrasset', 'Tazrouk'],
        'Tébessa': ['Ain Zerga', 'Bedjene', 'Bekkaria', 'Bir Dheheb', 'Bir El Ater', 'Bir Mokkadem', 'Boukhadra', 'Boulhaf Dyr', 'Cheria', 'El Aouinet', 'El Houidjbet', 'El Kouif', 'El Malabiod', 'El Meridj', 'El Mezeraa', 'El Ogla', 'El Ogla El Malha', 'Ferkane', 'Guorriguer', 'Hammamet', 'Morssot', 'Negrine', 'Ouenza', 'Oum Ali', 'Saf Saf El Ouesra', 'Stah Guentis', 'Tebessa', 'Telidjen'],
        'Tlemcen': ['Ain Fettah', 'Ain Fezza', 'Ain Ghoraba', 'Ain Kebira', 'Ain Nehala', 'Ain Tallout', 'Ain Youcef', 'Amieur', 'Azails', 'Bab El Assa', 'Beni Bahdel', 'Beni Boussaid', 'Beni Khaled', 'Beni Mester', 'Beni Ouarsous', 'Beni Smiel', 'Beni Snous', 'Bensekrane', 'Bouhlou', 'Bouihi', 'Chetouane', 'Dar Yaghmouracene', 'Djebala', 'El Aricha', 'El Fehoul', 'El Gor', 'Fellaoucene', 'Ghazaouet', 'Hammam Boughrara', 'Hennaya', 'Honaine', 'Maghnia', 'Mansourah', 'Marsa Ben Mhidi', 'Msirda Fouaga', 'Nedroma', 'Oued Chouly', 'Ouled Mimoun', 'Ouled Riyah', 'Remchi', 'Sabra', 'Sebbaa Chioukh', 'Sebdou', 'Sidi Abdelli', 'Sidi Djilali', 'Sidi Medjahed', 'Souahlia', 'Souani', 'Souk Tleta', 'Terny Beni Hediel', 'Tianet', 'Tlemcen', 'Zenata'],
        'Tiaret': ['Ain Bouchekif', 'Ain Deheb', 'Ain El Hadid', 'Ain Kermes', 'Ain Zarit', 'Bougara', 'Chehaima', 'Dahmouni', 'Djebilet Rosfa', 'Djillali Ben Amar', 'Faidja', 'Frenda', 'Guertoufa', 'Hamadia', 'Ksar Chellala', 'Madna', 'Mahdia', 'Mechraa Safa', 'Medrissa', 'Medroussa', 'Meghila', 'Mellakou', 'Nadorah', 'Naima', 'Oued Lilli', 'Rahouia', 'Rechaiga', 'Sebaine', 'Sebt', 'Serghine', 'Si Abdelghani', 'Sidi Abderrahmane', 'Sidi Ali Mellal', 'Sidi Bakhti', 'Sidi Hosni', 'Sougueur', 'Tagdemt', 'Takhemaret', 'Tiaret', 'Tidda', 'Tousnina', 'Zmalet El Emir Abdelkader'],
        'Tizi Ouzou': ['Abi Youcef', 'Aghribs', 'Agouni Gueghrane', 'Ain El Hammam', 'Ain Zaouia', 'Ait Aggouacha', 'Ait Bouaddou', 'Ait Boumehdi', 'Ait Chafaa', 'Ait Khellili', 'Ait Mahmoud', 'Ait Oumalou', 'Ait Toudert', 'Ait Yahia', 'Ait Yahia Moussa', 'Akbil', 'Akerrou', 'Assi Youcef', 'Azazga', 'Azeffoun', 'Beni Aissi', 'Beni Douala', 'Beni Yenni', 'Beni Zikki', 'Beni Zmenzer', 'Boghni', 'Boudjima', 'Bounouh', 'Bouzeguene', 'Djebel Aissa Mimoun', 'Draa Ben Khedda', 'Draa El Mizan', 'Freha', 'Frikat', 'Iboudrarene', 'Idjeur', 'Iferhounene', 'Ifigha', 'Iflissen', 'Illilten', 'Illoula Oumalou', 'Imsouhal', 'Irdjen', 'Larba Nath Irathen', 'Mkira', 'Maatkas', 'Makouda', 'Mechtras', 'Mekla', 'Mizrana', 'Ouacif', 'Ouadhias', 'Ouaguenoune', 'Sidi Naamane', 'Souamaa', 'Souk El Thenine', 'Tadmait', 'Tigzirt', 'Timizart', 'Tirmitine', 'Tizi Ghenif', 'Tizi Ntleta', 'Tizi Ouzou', 'Tizi Rached', 'Yakourene', 'Yatafene', 'Zekri'],
        'Alger': ['Ain Benian', 'Ain Taya', 'Alger Centre', 'Bab El Oued', 'Bab Ezzouar', 'Baba Hesen', 'Bachedjerah', 'Bains Romains', 'Baraki', 'Ben Aknoun', 'Beni Messous', 'Bir Mourad Rais', 'Bir Touta', 'Birkhadem', 'Bologhine Ibnou Ziri', 'Bordj El Bahri', 'Bordj El Kiffan', 'Bourouba', 'Bouzareah', 'Casbah', 'Cheraga', 'Dar El Beida', 'Dely Ibrahim', 'Djasr Kasentina', 'Douira', 'Draria', 'El Achour', 'El Biar', 'El Harrach', 'El Madania', 'El Magharia', 'El Merssa', 'El Mouradia', 'Herraoua', 'Hussein Dey', 'Hydra', 'Kheraisia', 'Kouba', 'Les Eucalyptus', 'Maalma', 'Mohamed Belouzdad', 'Mohammadia', 'Oued Koriche', 'Oued Smar', 'Ouled Chebel', 'Ouled Fayet', 'Rahmania', 'Rais Hamidou', 'Reghaia', 'Rouiba', 'Sehaoula', 'Setaouali', 'Sidi Mhamed', 'Sidi Moussa', 'Souidania', 'Tessala El Merdja', 'Zeralda'],
        'Djelfa': ['Ain Chouhada', 'Ain El Ibel', 'Ain Fekka', 'Ain Maabed', 'Ain Oussera', 'Amourah', 'Benhar', 'Benyagoub', 'Birine', 'Bouira Lahdab', 'Charef', 'Dar Chioukh', 'Deldoul', 'Djelfa', 'Douis', 'El Guedid', 'El Idrissia', 'El Khemis', 'Faidh El Botma', 'Guernini', 'Guettara', 'Had Sahary', 'Hassi Bahbah', 'Hassi El Euch', 'Hassi Fedoul', 'M Liliha', 'Messaad', 'Moudjebara', 'Oum Laadham', 'Sed Rahal', 'Selmana', 'Sidi Baizid', 'Sidi Ladjel', 'Tadmit', 'Zaafrane', 'Zaccar'],
        'Jijel': ['Bordj Tahar', 'Boudria Beniyadjis', 'Bouraoui Belhadef', 'Boussif Ouled Askeur', 'Chahna', 'Chekfa', 'Djemaa Beni Habibi', 'Djimla', 'El Ancer', 'El Aouana', 'El Kennar Nouchfi', 'El Milia', 'Emir Abdelkader', 'Erraguene', 'Ghebala', 'Jijel', 'Khiri Oued Adjoul', 'Kouas', 'Oudjana', 'Ouled Rabah', 'Ouled Yahia Khadrouch', 'Selma Benziada', 'Settara', 'Sidi Abdelaziz', 'Sidi Marouf', 'Taher', 'Texena', 'Ziama Mansouria'],
        'Sétif': ['Ain Abessa', 'Ain Arnat', 'Ain Azel', 'Ain El Kebira', 'Ain Lahdjar', 'Ain Legradj', 'Ain Oulmane', 'Ain Roua', 'Ain Sebt', 'Ait Naoual Mezada', 'Ait Tizi', 'Amoucha', 'Babor', 'Bazer Sakra', 'Beidha Bordj', 'Bellaa', 'Beni Aziz', 'Beni Chebana', 'Beni Fouda', 'Beni Mouhli', 'Beni Ouartilane', 'Beni Oussine', 'Bir El Arch', 'Bir Haddada', 'Bouandas', 'Bougaa', 'Bousselam', 'Boutaleb', 'Dehamcha', 'Djemila', 'Draa Kebila', 'El Eulma', 'El Ouldja', 'El Ouricia', 'Guellal', 'Guelta Zerka', 'Guenzet', 'Guidjel', 'Hamam Soukhna', 'Hamma', 'Hammam Guergour', 'Harbil', 'Ksar El Abtal', 'Maaouia', 'Maouaklane', 'Mezloug', 'Oued El Barad', 'Ouled Addouane', 'Ouled Sabor', 'Ouled Si Ahmed', 'Ouled Tebben', 'Rosfa', 'Salah Bey', 'Serdj El Ghoul', 'Setif', 'Tachouda', 'Tala Ifacene', 'Taya', 'Tella', 'Tizi Nbechar'],
        'Saïda': ['Ain El Hadjar', 'Ain Sekhouna', 'Ain Soltane', 'Doui Thabet', 'El Hassasna', 'Hounet', 'Maamora', 'Moulay Larbi', 'Ouled Brahim', 'Ouled Khaled', 'Saida', 'Sidi Ahmed', 'Sidi Amar', 'Sidi Boubekeur', 'Tircine', 'Youb'],
        'Skikda': ['Ain Bouziane', 'Ain Charchar', 'Ain Kechera', 'Ain Zouit', 'Azzaba', 'Bekkouche Lakhdar', 'Ben Azzouz', 'Beni Bechir', 'Beni Oulbane', 'Beni Zid', 'Bin El Ouiden', 'Bouchetata', 'Cheraia', 'Collo', 'Djendel Saadi Mohamed', 'El Arrouch', 'El Ghedir', 'El Hadaiek', 'El Marsa', 'Emjez Edchich', 'Es Sebt', 'Filfila', 'Hamadi Krouma', 'Kanoua', 'Kerkera', 'Khenag Mayoum', 'Oued Zhour', 'Ouldja Boulbalout', 'Ouled Attia', 'Ouled Habbeba', 'Oum Toub', 'Ramdane Djamel', 'Salah Bouchaour', 'Sidi Mezghiche', 'Skikda', 'Tamalous', 'Zerdezas', 'Zitouna'],
        'Sidi Bel Abbès': ['Ain Adden', 'Ain El Berd', 'Ain Kada', 'Ain Thrid', 'Ain Tindamine', 'Amarnas', 'Badredine El Mokrani', 'Belarbi', 'Ben Badis', 'Benachiba Chelia', 'Bir El Hammam', 'Boudjebaa El Bordj', 'Boukhanafis', 'Chetouane Belaila', 'Dhaya', 'El Hacaiba', 'Hassi Dahou', 'Hassi Zahana', 'Lamtar', 'Mcid', 'Makedra', 'Marhoum', 'Merine', 'Mezaourou', 'Mostefa Ben Brahim', 'Moulay Slissen', 'Oued Sebaa', 'Oued Sefioun', 'Oued Taourira', 'Ras El Ma', 'Redjem Demouche', 'Sehala Thaoura', 'Sfissef', 'Sidi Ali Benyoub', 'Sidi Ali Boussidi', 'Sidi Bel Abbes', 'Sidi Brahim', 'Sidi Chaib', 'Sidi Dahou Zairs', 'Sidi Hamadouche', 'Sidi Khaled', 'Sidi Lahcene', 'Sidi Yacoub', 'Tabia', 'Tafissour', 'Taoudmout', 'Teghalimet', 'Telagh', 'Tenira', 'Tessala', 'Tilmouni', 'Zerouala'],
        'Annaba': ['Ain Berda', 'Annaba', 'Berrahel', 'Chetaibi', 'Cheurfa', 'El Bouni', 'El Hadjar', 'Eulma', 'Oued El Aneb', 'Seraidi', 'Sidi Amar', 'Treat'],
        'Guelma': ['Ain Ben Beida', 'Ain Hessania', 'Ain Larbi', 'Ain Makhlouf', 'Ain Reggada', 'Belkheir', 'Ben Djarah', 'Beni Mezline', 'Bordj Sabat', 'Bou Hachana', 'Bou Hamdane', 'Bouati Mahmoud', 'Bouchegouf', 'Bouhamra Ahmed', 'Dahouara', 'Djeballah Khemissi', 'El Fedjoudj', 'Guelaat Bou Sbaa', 'Guelma', 'Hamam Debagh', 'Hammam Nbail', 'Heliopolis', 'Khezara', 'Medjez Amar', 'Medjez Sfa', 'Nechmaya', 'Oued Cheham', 'Oued Fragha', 'Oued Zenati', 'Ras El Agba', 'Roknia', 'Sellaoua Announa', 'Sidi Sandel', 'Tamlouka'],
        'Constantine': ['Ain Abid', 'Ain Smara', 'Ben Badis', 'Beni Hamidene', 'Constantine', 'Didouche Mourad', 'El Khroub', 'Hamma Bouziane', 'Ibn Ziad', 'Messaoud Boujeriou', 'Ouled Rahmouni', 'Zighoud Youcef'],
        'Médéa': ['Ain Boucif', 'Ain Ouksir', 'Aissaouia', 'Aziz', 'Baata', 'Ben Chicao', 'Beni Slimane', 'Berrouaghia', 'Bir Ben Laabed', 'Boghar', 'Bouaiche', 'Bouaichoune', 'Bouchrahil', 'Boughzoul', 'Bouskene', 'Chabounia', 'Chelalet El Adhaoura', 'Cheniguel', 'Damiat', 'Derrag', 'Deux Bassins', 'Djouab', 'Draa Essamar', 'El Azizia', 'El Guelbelkebir', 'El Hamdania', 'El Omaria', 'El Ouinet', 'Hannacha', 'Kef Lakhdar', 'Khams Djouamaa', 'Ksar El Boukhari', 'Maghraoua', 'Medea', 'Medjebar', 'Meftaha', 'Mezerana', 'Mihoub', 'Ouamri', 'Oued Harbil', 'Ouled Antar', 'Ouled Bouachra', 'Ouled Brahim', 'Ouled Deid', 'Ouled Hellal', 'Ouled Maaref', 'Oum El Djellil', 'Ouzera', 'Rebaia', 'Saneg', 'Sedraya', 'Seghouane', 'Si Mahdjoub', 'Sidi Demed', 'Sidi Naamane', 'Sidi Rabie', 'Sidi Zahar', 'Sidi Ziane', 'Souagui', 'Tablat', 'Tafraout', 'Tamesguida', 'Tletat Ed Douair', 'Zoubiria'],
        'Mostaganem': ['Achaacha', 'Ain Boudinar', 'Ain Nouissy', 'Ain Sidi Cherif', 'Ain Tedles', 'Benabdelmalek Ramdane', 'Bouguirat', 'Fornaka', 'Hadjadj', 'Hassi Mameche', 'Hassiane', 'Khadra', 'Kheir Eddine', 'Mansourah', 'Mazagran', 'Mesra', 'Mostaganem', 'Nekmaria', 'Oued El Kheir', 'Ouled Boughalem', 'Ouled Maalah', 'Safsaf', 'Sayada', 'Sidi Ali', 'Sidi Belaattar', 'Sidi Lakhdar', 'Sirat', 'Souaflia', 'Sour', 'Stidia', 'Tazgait', 'Touahria'],
        'MSila': ['Ain El Hadjel', 'Ain El Melh', 'Ain Fares', 'Ain Khadra', 'Ain Rich', 'Belaiba', 'Ben Srour', 'Beni Ilmane', 'Benzouh', 'Berhoum', 'Bir Foda', 'Bou Saada', 'Bouti Sayeh', 'Chellal', 'Dehahna', 'Djebel Messaad', 'El Hamel', 'El Houamed', 'Hammam Dalaa', 'Khettouti Sed El Jir', 'Khoubana', 'Mcif', 'Msila', 'Mtarfa', 'Maadid', 'Maarif', 'Magra', 'Medjedel', 'Menaa', 'Mohamed Boudiaf', 'Ouanougha', 'Ouled Addi Guebala', 'Ouled Derradj', 'Ouled Madhi', 'Ouled Mansour', 'Ouled Sidi Brahim', 'Ouled Slimane', 'Oulteme', 'Sidi Aissa', 'Sidi Ameur', 'Sidi Hadjeres', 'Sidi Mhamed', 'Slim', 'Souamaa', 'Tamsa', 'Tarmount', 'Zarzour'],
        'Mascara': ['Ain Fares', 'Ain Fekan', 'Ain Ferah', 'Ain Frass', 'Alaimia', 'Aouf', 'Benian', 'Bou Henni', 'Bouhanifia', 'Chorfa', 'El Bordj', 'El Gaada', 'El Ghomri', 'El Gueitena', 'El Hachem', 'El Keurt', 'El Mamounia', 'El Menaouer', 'Ferraguig', 'Froha', 'Gharrous', 'Ghriss', 'Guerdjoum', 'Hacine', 'Khalouia', 'Makhda', 'Maoussa', 'Mascara', 'Matemore', 'Mocta Douz', 'Mohammadia', 'Nesmot', 'Oggaz', 'Oued El Abtal', 'Oued Taria', 'Ras El Ain Amirouche', 'Sedjerara', 'Sehailia', 'Sidi Abdeldjebar', 'Sidi Abdelmoumene', 'Sidi Boussaid', 'Sidi Kada', 'Sig', 'Tighennif', 'Tizi', 'Zahana', 'Zelamta'],
        'Ouargla': ['Ain Beida', 'El Borma', 'Hassi Ben Abdellah', 'Hassi Messaoud', 'Ngoussa', 'Ouargla', 'Rouissat', 'Sidi Khouiled'],
        'Oran': ['Ain Biya', 'Ain Kerma', 'Ain Turk', 'Arzew', 'Ben Freha', 'Bethioua', 'Bir El Djir', 'Boufatis', 'Bousfer', 'Boutlelis', 'El Ancar', 'El Braya', 'El Kerma', 'Es Senia', 'Gdyel', 'Hassi Ben Okba', 'Hassi Bounif', 'Hassi Mefsoukh', 'Marsat El Hadjadj', 'Mers El Kebir', 'Messerghin', 'Oran', 'Oued Tlelat', 'Sidi Ben Yebka', 'Sidi Chami', 'Tafraoui'],
        'El Bayadh': ['Ain El Orak', 'Arbaouat', 'Boualem', 'Bougtoub', 'Boussemghoun', 'Brezina', 'Cheguig', 'Chellala', 'El Bayadh', 'El Biodh Sidi Cheikh', 'El Bnoud', 'El Kheither', 'El Mehara', 'Ghassoul', 'Kef El Ahmar', 'Krakda', 'Rogassa', 'Sidi Ameur', 'Sidi Slimane', 'Sidi Tifour', 'Stitten', 'Tousmouline'],
        'Illizi': ['Bordj Omar Driss', 'Debdeb', 'Illizi', 'In Amenas'],
        'Bordj Bou Arreridj': ['Ain Taghrout', 'Ain Tesra', 'Belimour', 'Ben Daoud', 'Bir Kasdali', 'Bordj Bou Arreridj', 'Bordj Ghdir', 'Bordj Zemora', 'Colla', 'Djaafra', 'El Ach', 'El Achir', 'El Anseur', 'El Hamadia', 'El Mhir', 'El Main', 'Ghilassa', 'Haraza', 'Hasnaoua', 'Khelil', 'Ksour', 'Mansoura', 'Medjana', 'Ouled Brahem', 'Ouled Dahmane', 'Ouled Sidi Brahim', 'Rabta', 'Ras El Oued', 'Sidi Embarek', 'Tafreg', 'Taglait', 'Teniet En Nasr', 'Tesmart', 'Tixter'],
        'Boumerdès': ['Afir', 'Ammal', 'Baghlia', 'Ben Choud', 'Beni Amrane', 'Bordj Menaiel', 'Boudouaou', 'Boudouaou El Bahri', 'Boumerdes', 'Bouzegza Keddara', 'Chabet El Ameur', 'Corso', 'Dellys', 'Djinet', 'El Kharrouba', 'Hammedi', 'Isser', 'Khemis El Khechna', 'Larbatache', 'Leghata', 'Naciria', 'Ouled Aissa', 'Ouled Hedadj', 'Ouled Moussa', 'Si Mustapha', 'Sidi Daoud', 'Souk El Haad', 'Taourga', 'Thenia', 'Tidjelabine', 'Timezrit', 'Zemmouri'],
        'El Tarf': ['Ain El Assel', 'Ain Kerma', 'Asfour', 'Ben M Hidi', 'Berrihane', 'Besbes', 'Bougous', 'Bouhadjar', 'Bouteldja', 'Chebaita Mokhtar', 'Chefia', 'Chihani', 'Drean', 'Echatt', 'El Aioun', 'El Kala', 'El Tarf', 'Hammam Beni Salah', 'Lac Des Oiseaux', 'Oued Zitoun', 'Raml Souk', 'Souarekh', 'Zerizer', 'Zitouna'],
        'Tindouf': ['Oum El Assel', 'Tindouf'],
        'Tissemsilt': ['Ammari', 'Beni Chaib', 'Beni Lahcene', 'Bordj Bounaama', 'Bordj El Emir Abdelkader', 'Bou Caid', 'Khemisti', 'Larbaa', 'Lardjem', 'Layoune', 'Lazharia', 'Maacem', 'Melaab', 'Ouled Bessem', 'Sidi Abed', 'Sidi Boutouchent', 'Sidi Lantri', 'Sidi Slimane', 'Tamellalet', 'Theniet El Had', 'Tissemsilt', 'Youssoufia'],
        'El Oued': ['Bayadha', 'Ben Guecha', 'Debila', 'Douar El Maa', 'El Ogla', 'El Oued', 'Guemar', 'Hamraia', 'Hassani Abdelkrim', 'Hassi Khalifa', 'Kouinine', 'Magrane', 'Mih Ouansa', 'Nakhla', 'Oued El Alenda', 'Ourmes', 'Reguiba', 'Robbah', 'Sidi Aoun', 'Taghzout', 'Taleb Larbi', 'Trifaoui'],
        'Khenchela': ['Ain Touila', 'Babar', 'Baghai', 'Bouhmama', 'Chelia', 'Cherchar', 'Djellal', 'El Hamma', 'El Mahmal', 'El Oueldja', 'Ensigha', 'Kais', 'Khenchela', 'Khirane', 'Msara', 'Mtoussa', 'Ouled Rechache', 'Remila', 'Tamza', 'Taouzianat', 'Yabous'],
        'Souk Ahras': ['Ain Soltane', 'Ain Zana', 'Bir Bouhouche', 'Drea', 'Haddada', 'Hanencha', 'Khedara', 'Khemissa', 'Mdaourouche', 'Machroha', 'Merahna', 'Oued Kebrit', 'Ouled Driss', 'Ouled Moumen', 'Oum El Adhaim', 'Quillen', 'Ragouba', 'Safel El Ouiden', 'Sedrata', 'Sidi Fredj', 'Souk Ahras', 'Taoura', 'Terraguelt', 'Tiffech', 'Zaarouria', 'Zouabi'],
        'Tipaza': ['Aghbal', 'Ahmer El Ain', 'Ain Tagourait', 'Attatba', 'Beni Mileuk', 'Bou Haroun', 'Bou Ismail', 'Bourkika', 'Chaiba', 'Cherchell', 'Damous', 'Douaouda', 'Fouka', 'Gouraya', 'Hadjout', 'Hadjret Ennous', 'Khemisti', 'Kolea', 'Larhat', 'Menaceur', 'Merad', 'Messelmoun', 'Nador', 'Sidi Amar', 'Sidi Ghiles', 'Sidi Rached', 'Sidi Semiane', 'Tipaza'],
        'Mila': ['Ahmed Rachedi', 'Ain Beida Harriche', 'Ain Mellouk', 'Ain Tine', 'Amira Arres', 'Benyahia Abderrahmane', 'Bouhatem', 'Chelghoum Laid', 'Chigara', 'Derrahi Bousselah', 'El Mechira', 'Elayadi Barbes', 'Ferdjioua', 'Grarem Gouga', 'Hamala', 'Mila', 'Minar Zarza', 'Oued Athmenia', 'Oued Endja', 'Oued Seguen', 'Ouled Khalouf', 'Rouached', 'Sidi Khelifa', 'Sidi Merouane', 'Tadjenanet', 'Tassadane Haddada', 'Teleghma', 'Terrai Bainem', 'Tessala', 'Tiberguent', 'Yahia Beniguecha', 'Zeghaia'],
        'Aïn Defla': ['Ain Benian', 'Ain Bouyahia', 'Ain Defla', 'Ain Lechiakh', 'Ain Soltane', 'Ain Tork', 'Arib', 'Barbouche', 'Bathia', 'Belaas', 'Ben Allal', 'Bir Ould Khelifa', 'Bordj Emir Khaled', 'Boumedfaa', 'Bourached', 'Djelida', 'Djemaa Ouled Cheikh', 'Djendel', 'El Abadia', 'El Amra', 'El Attaf', 'El Maine', 'Hammam Righa', 'Hassania', 'Hoceinia', 'Khemis Miliana', 'Mekhatria', 'Miliana', 'Oued Chorfa', 'Oued Djemaa', 'Rouina', 'Sidi Lakhdar', 'Tacheta Zegagha', 'Tarik Ibn Ziad', 'Tiberkanine', 'Zeddine'],
        'Naâma': ['Ain Ben Khelil', 'Ain Safra', 'Assela', 'Djeniane Bourzeg', 'El Biod', 'Kasdir', 'Makman Ben Amer', 'Mecheria', 'Moghrar', 'Naama', 'Sfissifa', 'Tiout'],
        'Aïn Témouchent': ['Aghlal', 'Ain El Arbaa', 'Ain Kihal', 'Ain Temouchent', 'Ain Tolba', 'Aoubellil', 'Beni Saf', 'Bouzedjar', 'Chaabat El Ham', 'Chentouf', 'El Amria', 'El Malah', 'El Messaid', 'Emir Abdelkader', 'Hammam Bouhadjar', 'Hassasna', 'Hassi El Ghella', 'Oued Berkeche', 'Oued Sebbah', 'Ouled Boudjemaa', 'Ouled Kihal', 'Oulhaca El Gheraba', 'Sidi Ben Adda', 'Sidi Boumediene', 'Sidi Ouriache', 'Sidi Safi', 'Tamzoura', 'Terga'],
        'Ghardaïa': ['Berriane', 'Bounoura', 'Dhayet Bendhahoua', 'El Atteuf', 'El Guerrara', 'Ghardaia', 'Mansoura', 'Metlili', 'Sebseb', 'Zelfana'],
        'Relizane': ['Ain Rahma', 'Ain Tarek', 'Ammi Moussa', 'Belaassel Bouzagza', 'Bendaoud', 'Beni Dergoun', 'Beni Zentis', 'Dar Ben Abdelah', 'Djidiouia', 'El Guettar', 'El Hmadna', 'El Hassi', 'El Matmar', 'El Ouldja', 'Had Echkalla', 'Hamri', 'Kalaa', 'Lahlef', 'Mazouna', 'Mediouna', 'Mendes', 'Merdja Sidi Abed', 'Ouarizane', 'Oued El Djemaa', 'Oued Essalem', 'Oued Rhiou', 'Ouled Aiche', 'Ouled Sidi Mihoub', 'Ramka', 'Relizane', 'Sidi Khettab', 'Sidi Lazreg', 'Sidi Mhamed Benali', 'Sidi Mhamed Benaouda', 'Sidi Saada', 'Souk El Had', 'Yellel', 'Zemmoura']
    };

    // Create wilaya select element
    const wilayaSelect = document.createElement('select');
    wilayaSelect.name = 'extra_fields[custom_field_RHwHakc5cRSN1W4j]';
    wilayaSelect.id = 'wilaya-select';
    wilayaSelect.className = 'form-control';

    // Add default option
    const defaultOption = document.createElement('option');
    defaultOption.value = '';
    defaultOption.textContent = 'Sélectionnez une wilaya';
    wilayaSelect.appendChild(defaultOption);

    // Add wilaya options
    Object.keys(wilayaData).forEach(wilaya => {
        const option = document.createElement('option');
        option.value = wilaya;
        option.textContent = wilaya;
        wilayaSelect.appendChild(option);
    });

    // Create commune select element
    const communeSelect = document.createElement('select');
    communeSelect.name = 'extra_fields[custom_field_MtcWLJO1uGmfFgV0]';
    communeSelect.id = 'commune-select';
    communeSelect.className = 'form-control';
    communeSelect.disabled = true;

    // Add default option for commune
    const defaultCommuneOption = document.createElement('option');
    defaultCommuneOption.value = '';
    defaultCommuneOption.textContent = 'Sélectionnez une commune';
    communeSelect.appendChild(defaultCommuneOption);

    // Create delivery type select
    const deliveryTypeSelect = document.createElement('select');
    deliveryTypeSelect.name = 'extra_fields[custom_field_jseeWqwCUI6eEABf]';
    deliveryTypeSelect.id = 'delivery-type-select';
    deliveryTypeSelect.className = 'form-control';

    // Add delivery type options
    const deliveryTypes = [
        { value: 'standard', text: 'Livraison Standard' },
        { value: 'express', text: 'Livraison Express' }
    ];

    deliveryTypes.forEach(type => {
        const option = document.createElement('option');
        option.value = type.value;
        option.textContent = type.text;
        deliveryTypeSelect.appendChild(option);
    });

    // Add event listener for wilaya selection
    wilayaSelect.addEventListener('change', function() {
        const selectedWilaya = this.value;
        communeSelect.disabled = !selectedWilaya;
        
        // Clear existing commune options
        while (communeSelect.options.length > 1) {
            communeSelect.remove(1);
        }

        if (selectedWilaya && wilayaData[selectedWilaya]) {
            // Add communes for the selected wilaya
            wilayaData[selectedWilaya].forEach(commune => {
                const option = document.createElement('option');
                option.value = commune;
                option.textContent = commune;
                communeSelect.appendChild(option);
            });
        }
    });

    // Find the container where you want to add these elements
    const container = document.querySelector('.checkout-form'); // Adjust this selector based on your page structure

    if (container) {
        // Create labels and wrappers
        const wilayaWrapper = document.createElement('div');
        wilayaWrapper.className = 'form-group';
        const wilayaLabel = document.createElement('label');
        wilayaLabel.textContent = 'Wilaya';
        wilayaLabel.htmlFor = 'wilaya-select';
        wilayaWrapper.appendChild(wilayaLabel);
        wilayaWrapper.appendChild(wilayaSelect);

        const communeWrapper = document.createElement('div');
        communeWrapper.className = 'form-group';
        const communeLabel = document.createElement('label');
        communeLabel.text
        
        </script>