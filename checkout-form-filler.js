<script>
// Inject this script on the checkout page
document.addEventListener('DOMContentLoaded', function() {
    // Delivery prices data
    const deliveryPrices = {
        'Adrar': {home: 1500, desk: 700},
        'Chlef': {home: 650, desk: 300},
        'Laghouat': {home: 1000, desk: 500},
        'Oum El Bouaghi': {home: 850, desk: 350},
        'Batna': {home: 800, desk: 350},
        'Béjaïa': {home: 800, desk: 350},
        'Biskra': {home: 1000, desk: 500},
        'Béchar': {home: 1200, desk: 600},
        '<PERSON>lida': {home: 800, desk: 350},
        '<PERSON><PERSON>ra': {home: 800, desk: 350},
        'Tamanrasset': {home: 2000, desk: 1000},
        'Tébessa': {home: 900, desk: 450},
        'Tlemcen': {home: 800, desk: 350},
        'Tiaret': {home: 800, desk: 350},
        'Tizi O<PERSON>': {home: 800, desk: 350},
        'Alger': {home: 700, desk: 300},
        '<PERSON><PERSON><PERSON><PERSON>': {home: 1000, desk: 500},
        '<PERSON><PERSON><PERSON>': {home: 850, desk: 350},
        'Sétif': {home: 800, desk: 350},
        '<PERSON>ïda': {home: 800, desk: 350},
        'Skikda': {home: 800, desk: 350},
        'Sidi Bel Abbès': {home: 800, desk: 350},
        'Annaba': {home: 850, desk: 350},
        'Guelma': {home: 900, desk: 450},
        'Constantine': {home: 800, desk: 350},
        'Médéa': {home: 800, desk: 350},
        'Mostaganem': {home: 800, desk: 350},
        'MSila': {home: 850, desk: 350},
        'Mascara': {home: 800, desk: 350},
        'Ouargla': {home: 1100, desk: 500},
        'Oran': {home: 800, desk: 350},
        'El Bayadh': {home: 1200, desk: 600},
        'Illizi': {home: 1950, desk: 1000},
        'Bordj Bou Arreridj': {home: 800, desk: 350},
        'Boumerdès': {home: 750, desk: 350},
        'El Tarf': {home: 900, desk: 450},
        'Tindouf': {home: 1700, desk: 850},
        'Tissemsilt': {home: 800, desk: 350},
        'El Oued': {home: 1100, desk: 500},
        'Khenchela': {home: 850, desk: 350},
        'Souk Ahras': {home: 900, desk: 450},
        'Tipaza': {home: 800, desk: 350},
        'Mila': {home: 800, desk: 350},
        'Aïn Defla': {home: 700, desk: 300},
        'Naâma': {home: 1200, desk: 600},
        'Aïn Témouchent': {home: 800, desk: 350},
        'Ghardaïa': {home: 1100, desk: 500},
        'Relizane': {home: 550, desk: 250}
    };

    // Stopdesk locations data
    const stopDeskLocations = {
        'Adrar': {
            'Adrar': 'Cité les palmier en face l\'hopital'
        },
        'Chlef': {
            'Chlef': 'Rue Lac des Forêts (À côté du CNRC)'
        },
        'Laghouat': {
            'Laghouat': 'Cité Al Ouiam (En face la mosquée Hammani)',
            'Aflou': 'Rue Al-Gaada, à côté de la boulangerie Belkhair'
        },
        'Oum El Bouaghi': {
            'Ain Mlila': 'Ain Mlila Cité 12 logements en face CEM belaabed',
            'Oum El Bouaghi': 'Cité 176 logements LSP Batiment 13'
        },
        'Batna': {
            'Batna': 'Cité meddour kchida en face les batiments 500',
            'Barika': 'Quartier CHAABANI, en face notaire Bachir Farhani, a coté Algerie Telecom'
        },
        'Béjaïa': {
            'Bejaia': 'Rue des frères Tabet, à 20 mètres de l\'hôtel Golden H, en face la nouvelle promotion nid d\'abeille',
            'Akbou': 'Rue hibouche – arafou En face de djurdjura cars et alliance assurance'
        },
        'Biskra': {
            'Biskra': 'Cité 70 logement block 04 devant hôtel Morris'
        },
        'Béchar': {
            'Bechar': 'Cité 622 Logement Al Badr N°02 - Bloc 52 (derrière la radio EL SAOURA / En face la protection civile)'
        },
        'Blida': {
            'Blida': 'El ramoule à côté de la nouvelle gare routière',
            'Boufarik': 'La résidence Belkbir en face la salle des fêtes Layalina'
        },
        'Bouira': {
            'Bouira': 'Villa hamzaoui, ammar khodja, bouira'
        },
        'Tamanrasset': {
            'Tamanrasset': 'مولاي عومار طريق دائرة مقابل العشاب – مالطا'
        },
        'Tébessa': {
            'Tébessa': 'Boulvard Houari Boumedien (proche de la banque AGB)'
        },
        'Tlemcen': {
            'Tlemcen': 'Les Dhalias 426 El kiffane',
            'Maghnia': 'Ouled ben saber, À côté restaurant Rais'
        },
        'Tiaret': {
            'Tiaret': 'Cité 180 logements CNT local 01 N° 63'
        },
        'Tizi Ouzou': {
            'Tizi Ouzou': 'Cité 450 Logements, Nouvelle Ville en face la salle des fêtes Lilya',
            'Azazga': 'Route nationale N° 12 Taddart'
        },
        'Alger': {
            'Bir Mourad Raïs': '02, Lotissement Beau Séjour',
            'Bab Ezzouar': 'Cité 2038 Logements - Bâtiment 43 - RDC',
            'Chéraga': 'Place Iben Badis N° 03 - RDC',
            'Reghaia': '822 Logements LPP Amirouche Bâtiment A7 N°04 rez-de-chaussée',
            'Alger Centre': '22 Rue Hocine BELADJEL, Sacré-Cœur (En face la banque BADR)',
            'Baba Hassen': 'Cité Cherchali Boualam, À côté de croissant rouge',
            'Baraki': 'Route de Larbaâ, entre la mosquée El Bachir El Ibrahimi et le commissariat',
            'Douzi': 'Devant clinique médicale, En face École Hilal School'
        },
        'Djelfa': {
            'Djelfa': 'Cité BOUTRIFIS en face la gendarmerie',
            'Ain Ouassara': 'Quartier Mohamed Boudiaf, Section 23, Local n° 30 Bisi'
        },
        'Jijel': {
            'Jijel': 'Rue 26, Avenue Kaoula Mokhtar, Cité sans interdit, Hay IDARI'
        },
        'Sétif': {
            'Sétif': 'Cité Mesoudi Edhouadi 1014-614 Logement (En face la gare Didouche Mourad)',
            'El Eulma': 'Cité Tassahoumi, 160 Logements - Bâtiment 11, Caffé Wahib',
            'Ain Oulmene': 'En face CEM Douhil Abdul Hamid',
            'Guidjel': 'La zone industrielle, en face du groupe SADI et à côté de la moussala d\'El Takwa'
        },
        'Saïda': {
            'Saida': 'Cité Riad en face Maison de l\'Environnement'
        },
        'Skikda': {
            'Skikda': 'Rue Mohammed Namou, en face la direction Sonelgaz Fobor, la montée de Hammam Deradji'
        },
        'Sidi Bel Abbès': {
            'Sidi Bel Abbès': 'Rue CPR, En face Masjid El Ansar - حي بني عامر ، مقابل مسجد الانصار'
        },
        'Annaba': {
            'Annaba': 'Rue Djemila, Saint Claud (À côté de la mosqué Badr)',
            'El Bouni': 'Fractionnement de la Bouni 2, zone urbaine n° 43, rez-de-chaussée, section 40, groupe de propriété 02'
        },
        'Guelma': {
            'Guelma': 'Cité 19 Juin - Numéro 02, en face marché Elbaraka'
        },
        'Constantine': {
            'Zouaghi': 'Cité Tlemcen Zouaghi (En face de la gendarmerie)',
            'Ali Mendjeli': 'En face de Sarl Natura pro Algérie/entre deux salles des fêtés el baraka et méga',
            'Constantine': 'Rue Bouleli Ahcéne BELLE VUE à côté de la banque BNP Paribas'
        },
        'Médéa': {
            'Médéa': 'Cité Ennasr (Près du pôle universitaire et Sonelgaz)'
        },
        'Mostaganem': {
            'Mostaganem': 'La pépinière en face la glacière juste à côté de la libraire BENALIOUA (cité AKID AMIROUCHE boulevard NAFOUSSI OTHMAN)'
        },
        'MSila': {
            'Msila': 'Rue Ichbilia (En face l\'université de M\'Sila)',
            'Bousaada': 'Cité El Bader (ESTTIH) à côté de l\'annexe de l\'APC'
        },
        'Mascara': {
            'Mohammadia': 'Rue Larbi Ben M\'hidi, à côté de l\'agence de Barigou',
            'Mascara': 'Rue d\'Oran, colonel Amirouche, lot 112 N° 07 local 06, à côté de Hadj Grrifa'
        },
        'Ouargla': {
            'Ouargla': 'Sidi Abdelkader, derrière la maison de jeune'
        },
        'Oran': {
            'Maraval': 'Cité 1004 Logements SN B/T1 - Rez-De-Chaussée - Côté Droit 1, en face stade la radieuse Maraval',
            'Bir El Djir': 'Coopérative Immobilière Dar El Amel - N°14 - Local1 RC',
            'Gambita': 'Gambetta En face arrêt de bus 51 et 11 de (dispensaire cave-gay)'
        },
        'El Bayadh': {
            'El Bayadh': 'Cité jolie vue (Al-Mandhar Al-Jamil), à côté de la Direction de la distribution d\'électricité et de gaz'
        },
        'Illizi': {
            'Illizi': 'À côté de la wilaya / Près de boulangerie Ben Ziar'
        },
        'Bordj Bou Arreridj': {
            'Bordj Bou Arreridj': 'Rue Tabet Salah (Devant la maison de finance)'
        },
        'Boumerdès': {
            'Boumerdes': 'Cité Mimouza en face la piscine olympique',
            'Ouled Moussa': 'Zone industrielle d\'Ouled Moussa'
        },
        'El Tarf': {
            'El Tarf': 'City Center (Centre commerciale Zaydi 1er étage N°10)'
        },
        'Tindouf': {
            'Tindouf': 'Magasin N°03 cité Al-Qasabi, Section 14, Groupement Immobilier N° 165'
        },
        'Tissemsilt': {
            'Tissemsilt': 'Résidence Kaidi (Promotion), ancien arrêt des taxis'
        },
        'El Oued': {
            'El Oued': 'Cité Al-Rimal (la route menant au tribunal)'
        },
        'Khenchela': {
            'Khenchela': 'Rue du Poid Lourd, à côté de la clinique de dialyse Messai'
        },
        'Souk Ahras': {
            'Souk Ahras': 'En face radio Souk Ahras et laboratoire des analyses Taghest'
        },
        'Tipaza': {
            'Tipaza': 'Cité Mohammed Bougara, à côté de l\'école privée DAYA School'
        },
        'Mila': {
            'Mila': 'Château d\'eau en face protection civile',
            'Chelghoum El Aid': 'Rue 1er Novembre 1954 (Hôtel Rhumel)'
        },
        'Aïn Defla': {
            'Ain Defla': 'Cité Nadjem (En face de Taxi Aissam)',
            'Khemis Miliana': 'Cité Ahmed Ben Abd Allah'
        },
        'Naâma': {
            'Mécheria': 'Centre-Ville (En face de la Daira)'
        },
        'Aïn Témouchent': {
            'Aïn Témouchent': '22A cité des oliviers (en face du parking de la wilaya)'
        },
        'Ghardaïa': {
            'Ghardaia': 'Rue principale Hadj Messaoud, en face la branche municipale, Haj Masoud'
        },
        'Relizane': {
            'Relizane': 'Cité 31 Logements, en face de la justice, à côté de la banque Société Générale Algérie'
        }
    };

    // Wilaya and Commune data
    const wilayaData = {
        'Adrar': ['Adrar', 'Akabli', 'Aoulef', 'Bouda', 'Fenoughil', 'In Zghmir', 'Ouled Ahmed Timmi', 'Reggane', 'Sali', 'Sebaa', 'Tamantit', 'Tamest', 'Timekten', 'Tit', 'Tsabit', 'Zaouiet Kounta'],
    'Chlef': ['Abou El Hassan', 'Ain Merane', 'Benairia', 'Beni Bouattab', 'Beni Haoua', 'Beni Rached', 'Boukadir', 'Bouzeghaia', 'Breira', 'Chettia', 'Chlef', 'Dahra', 'El Hadjadj', 'El Karimia', 'El Marsa', 'Harchoun', 'Herenfa', 'Labiod Medjadja', 'Moussadek', 'Oued Fodda', 'Oued Goussine', 'Oued Sly', 'Ouled Abbes', 'Ouled Ben Abdelkader', 'Ouled Fares', 'Oum Drou', 'Sendjas', 'Sidi Abderrahmane', 'Sidi Akkacha', 'Sobha', 'Tadjena', 'Talassa', 'Taougrite', 'Tenes', 'Zeboudja'],
    'Laghouat': ['Aflou', 'Ain Mahdi', 'Ain Sidi Ali', 'Beidha', 'Benacer Benchohra', 'Brida', 'El Assafia', 'El Ghicha', 'El Haouaita', 'Gueltat Sidi Saad', 'Hadj Mechri', 'Hassi Delaa', 'Hassi Rmel', 'Kheneg', 'Ksar El Hirane', 'Laghouat', 'Oued Mzi', 'Oued Morra', 'Sebgag', 'Sidi Bouzid', 'Sidi Makhlouf', 'Tadjemout', 'Tadjrouna', 'Taouiala'],
    'Oum El Bouaghi': ['Ain Babouche', 'Ain Beida', 'Ain Diss', 'Ain Fekroune', 'Ain Kercha', 'Ain Mlila', 'Ain Zitoun', 'Behir Chergui', 'Berriche', 'Bir Chouhada', 'Dhala', 'El Amiria', 'El Belala', 'El Djazia', 'El Fedjoudj Boughrara Sa', 'El Harmilia', 'Fkirina', 'Hanchir Toumghani', 'Ksar Sbahi', 'Meskiana', 'Oued Nini', 'Ouled Gacem', 'Ouled Hamla', 'Ouled Zouai', 'Oum El Bouaghi', 'Rahia', 'Sigus', 'Souk Naamane', 'Zorg'],
    'Batna': ['Ain Djasser', 'Ain Touta', 'Ain Yagout', 'Arris', 'Azil Abedelkader', 'Barika', 'Batna', 'Beni Foudhala El Hakania', 'Bitam', 'Boulhilat', 'Boumagueur', 'Boumia', 'Bouzina', 'Chemora', 'Chir', 'Djerma', 'Djezzar', 'El Hassi', 'El Madher', 'Fesdis', 'Foum Toub', 'Ghassira', 'Gosbat', 'Guigba', 'Hidoussa', 'Ichmoul', 'Inoughissen', 'Kimmel', 'Ksar Bellezma', 'Larbaa', 'Lazrou', 'Lemsane', 'M Doukal', 'Maafa', 'Menaa', 'Merouana', 'N Gaous', 'Oued Chaaba', 'Oued El Ma', 'Oued Taga', 'Ouled Ammar', 'Ouled Aouf', 'Ouled Fadel', 'Ouled Sellem', 'Ouled Si Slimane', 'Ouyoun El Assafir', 'Rahbat', 'Ras El Aioun', 'Sefiane', 'Seggana', 'Seriana', 'T Kout', 'Talkhamt', 'Taxlent', 'Tazoult', 'Teniet El Abed', 'Tighanimine', 'Tigharghar', 'Tilatou', 'Timgad', 'Zanet El Beida'],
    'Béjaïa': ['Adekar', 'Ait Rzine', 'Ait Smail', 'Akbou', 'Akfadou', 'Amalou', 'Amizour', 'Aokas', 'Barbacha', 'Bejaia', 'Beni Dejllil', 'Beni Ksila', 'Beni Mallikeche', 'Benimaouche', 'Boudjellil', 'Bouhamza', 'Boukhelifa', 'Chellata', 'Chemini', 'Darghina', 'Dra El Caid', 'El Kseur', 'Fenaia Il Maten', 'Feraoun', 'Ighil Ali', 'Ighram', 'Kendira', 'Kherrata', 'Leflaye', 'Mcisna', 'Melbou', 'Oued Ghir', 'Ouzellaguene', 'Seddouk', 'Sidi Aich', 'Sidi Ayad', 'Smaoun', 'Souk El Tenine', 'Souk Oufella', 'Tala Hamza', 'Tamokra', 'Tamridjet', 'Taourit Ighil', 'Taskriout', 'Tazmalt', 'Tibane', 'Tichy', 'Tifra', 'Timezrit', 'Tinebdar', 'Tizi Nberber', 'Toudja'],
    'Biskra': ['Ain Naga', 'Ain Zaatout', 'Biskra', 'Bordj Ben Azzouz', 'Bouchagroun', 'Branis', 'Chetma', 'Djemorah', 'El Feidh', 'El Ghrous', 'El Hadjab', 'El Haouch', 'El Kantara', 'El Outaya', 'Foughala', 'Khenguet Sidi Nadji', 'Lichana', 'Lioua', 'Mchouneche', 'Mlili', 'Mekhadma', 'Meziraa', 'Oumache', 'Ourlal', 'Sidi Okba', 'Tolga', 'Zeribet El Oued'],
    'Béchar': ['Abadla', 'Bechar', 'Beni Ounif', 'Boukais', 'Erg Ferradj', 'Kenadsa', 'Lahmar', 'Mechraa H.boumediene', 'Meridja', 'Mogheul', 'Taghit'],
    'Blida': ['Ain Romana', 'Beni Mered', 'Beni Tamou', 'Benkhelil', 'Blida', 'Bouarfa', 'Boufarik', 'Bougara', 'Bouinan', 'Chebli', 'Chiffa', 'Chrea', 'Djebabra', 'El Affroun', 'Guerrouaou', 'Hammam Melouane', 'Larbaa', 'Meftah', 'Mouzaia', 'Oued Djer', 'Oued El Alleug', 'Ouled Slama', 'Ouled Yaich', 'Souhane', 'Souma'],
    'Bouira': ['Aghbalou', 'Ahl El Ksar', 'Ain Bessem', 'Ain El Hadjar', 'Ain Laloui', 'Ain Turk', 'Ait Laaziz', 'Aomar', 'Bechloul', 'Bir Ghbalou', 'Bordj Okhriss', 'Bouderbala', 'Bouira', 'Boukram', 'Chorfa', 'Dechmia', 'Dirah', 'Djebahia', 'El Adjiba', 'El Asnam', 'El Hachimia', 'El Hakimia', 'El Khabouzia', 'El Mokrani', 'Guerrouma', 'Hadjera Zerga', 'Haizer', 'Hanif', 'Kadiria', 'Lakhdaria', 'M Chedallah', 'Maala', 'Mamora', 'Mezdour', 'Oued El Berdi', 'Ouled Rached', 'Raouraoua', 'Ridane', 'Saharidj', 'Souk El Khemis', 'Sour El Ghozlane', 'Taghzout', 'Taguedite', 'Taourirt', 'Zbarbar'],
    'Tamanrasset': ['Abalessa', 'Ain Amguel', 'Idles', 'Tamanrasset', 'Tazrouk'],
    'Tébessa': ['Ain Zerga', 'Bedjene', 'Bekkaria', 'Bir Dheheb', 'Bir El Ater', 'Bir Mokkadem', 'Boukhadra', 'Boulhaf Dyr', 'Cheria', 'El Aouinet', 'El Houidjbet', 'El Kouif', 'El Malabiod', 'El Meridj', 'El Mezeraa', 'El Ogla', 'El Ogla El Malha', 'Ferkane', 'Guorriguer', 'Hammamet', 'Morssot', 'Negrine', 'Ouenza', 'Oum Ali', 'Saf Saf El Ouesra', 'Stah Guentis', 'Tebessa', 'Telidjen'],
    'Tlemcen': ['Ain Fettah', 'Ain Fezza', 'Ain Ghoraba', 'Ain Kebira', 'Ain Nehala', 'Ain Tallout', 'Ain Youcef', 'Amieur', 'Azails', 'Bab El Assa', 'Beni Bahdel', 'Beni Boussaid', 'Beni Khaled', 'Beni Mester', 'Beni Ouarsous', 'Beni Smiel', 'Beni Snous', 'Bensekrane', 'Bouhlou', 'Bouihi', 'Chetouane', 'Dar Yaghmouracene', 'Djebala', 'El Aricha', 'El Fehoul', 'El Gor', 'Fellaoucene', 'Ghazaouet', 'Hammam Boughrara', 'Hennaya', 'Honaine', 'Maghnia', 'Mansourah', 'Marsa Ben Mhidi', 'Msirda Fouaga', 'Nedroma', 'Oued Chouly', 'Ouled Mimoun', 'Ouled Riyah', 'Remchi', 'Sabra', 'Sebbaa Chioukh', 'Sebdou', 'Sidi Abdelli', 'Sidi Djilali', 'Sidi Medjahed', 'Souahlia', 'Souani', 'Souk Tleta', 'Terny Beni Hediel', 'Tianet', 'Tlemcen', 'Zenata'],
    'Tiaret': ['Ain Bouchekif', 'Ain Deheb', 'Ain El Hadid', 'Ain Kermes', 'Ain Zarit', 'Bougara', 'Chehaima', 'Dahmouni', 'Djebilet Rosfa', 'Djillali Ben Amar', 'Faidja', 'Frenda', 'Guertoufa', 'Hamadia', 'Ksar Chellala', 'Madna', 'Mahdia', 'Mechraa Safa', 'Medrissa', 'Medroussa', 'Meghila', 'Mellakou', 'Nadorah', 'Naima', 'Oued Lilli', 'Rahouia', 'Rechaiga', 'Sebaine', 'Sebt', 'Serghine', 'Si Abdelghani', 'Sidi Abderrahmane', 'Sidi Ali Mellal', 'Sidi Bakhti', 'Sidi Hosni', 'Sougueur', 'Tagdemt', 'Takhemaret', 'Tiaret', 'Tidda', 'Tousnina', 'Zmalet El Emir Abdelkader'],
    'Tizi Ouzou': ['Abi Youcef', 'Aghribs', 'Agouni Gueghrane', 'Ain El Hammam', 'Ain Zaouia', 'Ait Aggouacha', 'Ait Bouaddou', 'Ait Boumehdi', 'Ait Chafaa', 'Ait Khellili', 'Ait Mahmoud', 'Ait Oumalou', 'Ait Toudert', 'Ait Yahia', 'Ait Yahia Moussa', 'Akbil', 'Akerrou', 'Assi Youcef', 'Azazga', 'Azeffoun', 'Beni Aissi', 'Beni Douala', 'Beni Yenni', 'Beni Zikki', 'Beni Zmenzer', 'Boghni', 'Boudjima', 'Bounouh', 'Bouzeguene', 'Djebel Aissa Mimoun', 'Draa Ben Khedda', 'Draa El Mizan', 'Freha', 'Frikat', 'Iboudrarene', 'Idjeur', 'Iferhounene', 'Ifigha', 'Iflissen', 'Illilten', 'Illoula Oumalou', 'Imsouhal', 'Irdjen', 'Larba Nath Irathen', 'Mkira', 'Maatkas', 'Makouda', 'Mechtras', 'Mekla', 'Mizrana', 'Ouacif', 'Ouadhias', 'Ouaguenoune', 'Sidi Naamane', 'Souamaa', 'Souk El Thenine', 'Tadmait', 'Tigzirt', 'Timizart', 'Tirmitine', 'Tizi Ghenif', 'Tizi Ntleta', 'Tizi Ouzou', 'Tizi Rached', 'Yakourene', 'Yatafene', 'Zekri'],
    'Alger': ['Ain Benian', 'Ain Taya', 'Alger Centre', 'Bab El Oued', 'Bab Ezzouar', 'Baba Hesen', 'Bachedjerah', 'Bains Romains', 'Baraki', 'Ben Aknoun', 'Beni Messous', 'Bir Mourad Rais', 'Bir Touta', 'Birkhadem', 'Bologhine Ibnou Ziri', 'Bordj El Bahri', 'Bordj El Kiffan', 'Bourouba', 'Bouzareah', 'Casbah', 'Cheraga', 'Dar El Beida', 'Dely Ibrahim', 'Djasr Kasentina', 'Douira', 'Draria', 'El Achour', 'El Biar', 'El Harrach', 'El Madania', 'El Magharia', 'El Merssa', 'El Mouradia', 'Herraoua', 'Hussein Dey', 'Hydra', 'Kheraisia', 'Kouba', 'Les Eucalyptus', 'Maalma', 'Mohamed Belouzdad', 'Mohammadia', 'Oued Koriche', 'Oued Smar', 'Ouled Chebel', 'Ouled Fayet', 'Rahmania', 'Rais Hamidou', 'Reghaia', 'Rouiba', 'Sehaoula', 'Setaouali', 'Sidi Mhamed', 'Sidi Moussa', 'Souidania', 'Tessala El Merdja', 'Zeralda'],
    'Djelfa': ['Ain Chouhada', 'Ain El Ibel', 'Ain Fekka', 'Ain Maabed', 'Ain Oussera', 'Amourah', 'Benhar', 'Benyagoub', 'Birine', 'Bouira Lahdab', 'Charef', 'Dar Chioukh', 'Deldoul', 'Djelfa', 'Douis', 'El Guedid', 'El Idrissia', 'El Khemis', 'Faidh El Botma', 'Guernini', 'Guettara', 'Had Sahary', 'Hassi Bahbah', 'Hassi El Euch', 'Hassi Fedoul', 'M Liliha', 'Messaad', 'Moudjebara', 'Oum Laadham', 'Sed Rahal', 'Selmana', 'Sidi Baizid', 'Sidi Ladjel', 'Tadmit', 'Zaafrane', 'Zaccar'],
    'Jijel': ['Bordj Tahar', 'Boudria Beniyadjis', 'Bouraoui Belhadef', 'Boussif Ouled Askeur', 'Chahna', 'Chekfa', 'Djemaa Beni Habibi', 'Djimla', 'El Ancer', 'El Aouana', 'El Kennar Nouchfi', 'El Milia', 'Emir Abdelkader', 'Erraguene', 'Ghebala', 'Jijel', 'Khiri Oued Adjoul', 'Kouas', 'Oudjana', 'Ouled Rabah', 'Ouled Yahia Khadrouch', 'Selma Benziada', 'Settara', 'Sidi Abdelaziz', 'Sidi Marouf', 'Taher', 'Texena', 'Ziama Mansouria'],
  
    'Sétif': ['Ain Abessa', 'Ain Arnat', 'Ain Azel', 'Ain El Kebira', 'Ain Lahdjar', 'Ain Legradj', 'Ain Oulmane', 'Ain Roua', 'Ain Sebt', 'Ait Naoual Mezada', 'Ait Tizi', 'Amoucha', 'Babor', 'Bazer Sakra', 'Beidha Bordj', 'Bellaa', 'Beni Aziz', 'Beni Chebana', 'Beni Fouda', 'Beni Mouhli', 'Beni Ouartilane', 'Beni Oussine', 'Bir El Arch', 'Bir Haddada', 'Bouandas', 'Bougaa', 'Bousselam', 'Boutaleb', 'Dehamcha', 'Djemila', 'Draa Kebila', 'El Eulma', 'El Ouldja', 'El Ouricia', 'Guellal', 'Guelta Zerka', 'Guenzet', 'Guidjel', 'Hamam Soukhna', 'Hamma', 'Hammam Guergour', 'Harbil', 'Ksar El Abtal', 'Maaouia', 'Maouaklane', 'Mezloug', 'Oued El Barad', 'Ouled Addouane', 'Ouled Sabor', 'Ouled Si Ahmed', 'Ouled Tebben', 'Rosfa', 'Salah Bey', 'Serdj El Ghoul', 'Setif', 'Tachouda', 'Tala Ifacene', 'Taya', 'Tella', 'Tizi Nbechar'],
    'Saïda': ['Ain El Hadjar', 'Ain Sekhouna', 'Ain Soltane', 'Doui Thabet', 'El Hassasna', 'Hounet', 'Maamora', 'Moulay Larbi', 'Ouled Brahim', 'Ouled Khaled', 'Saida', 'Sidi Ahmed', 'Sidi Amar', 'Sidi Boubekeur', 'Tircine', 'Youb'],
    'Skikda': ['Ain Bouziane', 'Ain Charchar', 'Ain Kechera', 'Ain Zouit', 'Azzaba', 'Bekkouche Lakhdar', 'Ben Azzouz', 'Beni Bechir', 'Beni Oulbane', 'Beni Zid', 'Bin El Ouiden', 'Bouchetata', 'Cheraia', 'Collo', 'Djendel Saadi Mohamed', 'El Arrouch', 'El Ghedir', 'El Hadaiek', 'El Marsa', 'Emjez Edchich', 'Es Sebt', 'Filfila', 'Hamadi Krouma', 'Kanoua', 'Kerkera', 'Khenag Mayoum', 'Oued Zhour', 'Ouldja Boulbalout', 'Ouled Attia', 'Ouled Habbeba', 'Oum Toub', 'Ramdane Djamel', 'Salah Bouchaour', 'Sidi Mezghiche', 'Skikda', 'Tamalous', 'Zerdezas', 'Zitouna'],
    'Sidi Bel Abbès': ['Ain Adden', 'Ain El Berd', 'Ain Kada', 'Ain Thrid', 'Ain Tindamine', 'Amarnas', 'Badredine El Mokrani', 'Belarbi', 'Ben Badis', 'Benachiba Chelia', 'Bir El Hammam', 'Boudjebaa El Bordj', 'Boukhanafis', 'Chetouane Belaila', 'Dhaya', 'El Hacaiba', 'Hassi Dahou', 'Hassi Zahana', 'Lamtar', 'Mcid', 'Makedra', 'Marhoum', 'Merine', 'Mezaourou', 'Mostefa Ben Brahim', 'Moulay Slissen', 'Oued Sebaa', 'Oued Sefioun', 'Oued Taourira', 'Ras El Ma', 'Redjem Demouche', 'Sehala Thaoura', 'Sfissef', 'Sidi Ali Benyoub', 'Sidi Ali Boussidi', 'Sidi Bel Abbes', 'Sidi Brahim', 'Sidi Chaib', 'Sidi Dahou Zairs', 'Sidi Hamadouche', 'Sidi Khaled', 'Sidi Lahcene', 'Sidi Yacoub', 'Tabia', 'Tafissour', 'Taoudmout', 'Teghalimet', 'Telagh', 'Tenira', 'Tessala', 'Tilmouni', 'Zerouala'],
    'Annaba': ['Ain Berda', 'Annaba', 'Berrahel', 'Chetaibi', 'Cheurfa', 'El Bouni', 'El Hadjar', 'Eulma', 'Oued El Aneb', 'Seraidi', 'Sidi Amar', 'Treat'],
    'Guelma': ['Ain Ben Beida', 'Ain Hessania', 'Ain Larbi', 'Ain Makhlouf', 'Ain Reggada', 'Belkheir', 'Ben Djarah', 'Beni Mezline', 'Bordj Sabat', 'Bou Hachana', 'Bou Hamdane', 'Bouati Mahmoud', 'Bouchegouf', 'Bouhamra Ahmed', 'Dahouara', 'Djeballah Khemissi', 'El Fedjoudj', 'Guelaat Bou Sbaa', 'Guelma', 'Hamam Debagh', 'Hammam Nbail', 'Heliopolis', 'Khezara', 'Medjez Amar', 'Medjez Sfa', 'Nechmaya', 'Oued Cheham', 'Oued Fragha', 'Oued Zenati', 'Ras El Agba', 'Roknia', 'Sellaoua Announa', 'Sidi Sandel', 'Tamlouka'],
    'Constantine': ['Ain Abid', 'Ain Smara', 'Ben Badis', 'Beni Hamidene', 'Constantine', 'Didouche Mourad', 'El Khroub', 'Hamma Bouziane', 'Ibn Ziad', 'Messaoud Boujeriou', 'Ouled Rahmouni', 'Zighoud Youcef'],
    'Médéa': ['Ain Boucif', 'Ain Ouksir', 'Aissaouia', 'Aziz', 'Baata', 'Ben Chicao', 'Beni Slimane', 'Berrouaghia', 'Bir Ben Laabed', 'Boghar', 'Bouaiche', 'Bouaichoune', 'Bouchrahil', 'Boughzoul', 'Bouskene', 'Chabounia', 'Chelalet El Adhaoura', 'Cheniguel', 'Damiat', 'Derrag', 'Deux Bassins', 'Djouab', 'Draa Essamar', 'El Azizia', 'El Guelbelkebir', 'El Hamdania', 'El Omaria', 'El Ouinet', 'Hannacha', 'Kef Lakhdar', 'Khams Djouamaa', 'Ksar El Boukhari', 'Maghraoua', 'Medea', 'Medjebar', 'Meftaha', 'Mezerana', 'Mihoub', 'Ouamri', 'Oued Harbil', 'Ouled Antar', 'Ouled Bouachra', 'Ouled Brahim', 'Ouled Deid', 'Ouled Hellal', 'Ouled Maaref', 'Oum El Djellil', 'Ouzera', 'Rebaia', 'Saneg', 'Sedraya', 'Seghouane', 'Si Mahdjoub', 'Sidi Demed', 'Sidi Naamane', 'Sidi Rabie', 'Sidi Zahar', 'Sidi Ziane', 'Souagui', 'Tablat', 'Tafraout', 'Tamesguida', 'Tletat Ed Douair', 'Zoubiria'],
    'Mostaganem': ['Achaacha', 'Ain Boudinar', 'Ain Nouissy', 'Ain Sidi Cherif', 'Ain Tedles', 'Benabdelmalek Ramdane', 'Bouguirat', 'Fornaka', 'Hadjadj', 'Hassi Mameche', 'Hassiane', 'Khadra', 'Kheir Eddine', 'Mansourah', 'Mazagran', 'Mesra', 'Mostaganem', 'Nekmaria', 'Oued El Kheir', 'Ouled Boughalem', 'Ouled Maalah', 'Safsaf', 'Sayada', 'Sidi Ali', 'Sidi Belaattar', 'Sidi Lakhdar', 'Sirat', 'Souaflia', 'Sour', 'Stidia', 'Tazgait', 'Touahria'],
    'MSila': ['Ain El Hadjel', 'Ain El Melh', 'Ain Fares', 'Ain Khadra', 'Ain Rich', 'Belaiba', 'Ben Srour', 'Beni Ilmane', 'Benzouh', 'Berhoum', 'Bir Foda', 'Bou Saada', 'Bouti Sayeh', 'Chellal', 'Dehahna', 'Djebel Messaad', 'El Hamel', 'El Houamed', 'Hammam Dalaa', 'Khettouti Sed El Jir', 'Khoubana', 'Mcif', 'Msila', 'Mtarfa', 'Maadid', 'Maarif', 'Magra', 'Medjedel', 'Menaa', 'Mohamed Boudiaf', 'Ouanougha', 'Ouled Addi Guebala', 'Ouled Derradj', 'Ouled Madhi', 'Ouled Mansour', 'Ouled Sidi Brahim', 'Ouled Slimane', 'Oulteme', 'Sidi Aissa', 'Sidi Ameur', 'Sidi Hadjeres', 'Sidi Mhamed', 'Slim', 'Souamaa', 'Tamsa', 'Tarmount', 'Zarzour'],
    'Mascara': ['Ain Fares', 'Ain Fekan', 'Ain Ferah', 'Ain Frass', 'Alaimia', 'Aouf', 'Benian', 'Bou Henni', 'Bouhanifia', 'Chorfa', 'El Bordj', 'El Gaada', 'El Ghomri', 'El Gueitena', 'El Hachem', 'El Keurt', 'El Mamounia', 'El Menaouer', 'Ferraguig', 'Froha', 'Gharrous', 'Ghriss', 'Guerdjoum', 'Hacine', 'Khalouia', 'Makhda', 'Maoussa', 'Mascara', 'Matemore', 'Mocta Douz', 'Mohammadia', 'Nesmot', 'Oggaz', 'Oued El Abtal', 'Oued Taria', 'Ras El Ain Amirouche', 'Sedjerara', 'Sehailia', 'Sidi Abdeldjebar', 'Sidi Abdelmoumene', 'Sidi Boussaid', 'Sidi Kada', 'Sig', 'Tighennif', 'Tizi', 'Zahana', 'Zelamta'],
    'Ouargla': ['Ain Beida', 'El Borma', 'Hassi Ben Abdellah', 'Hassi Messaoud', 'Ngoussa', 'Ouargla', 'Rouissat', 'Sidi Khouiled'],
    'Oran': ['Ain Biya', 'Ain Kerma', 'Ain Turk', 'Arzew', 'Ben Freha', 'Bethioua', 'Bir El Djir', 'Boufatis', 'Bousfer', 'Boutlelis', 'El Ancar', 'El Braya', 'El Kerma', 'Es Senia', 'Gdyel', 'Hassi Ben Okba', 'Hassi Bounif', 'Hassi Mefsoukh', 'Marsat El Hadjadj', 'Mers El Kebir', 'Messerghin', 'Oran', 'Oued Tlelat', 'Sidi Ben Yebka', 'Sidi Chami', 'Tafraoui'],
    'El Bayadh': ['Ain El Orak', 'Arbaouat', 'Boualem', 'Bougtoub', 'Boussemghoun', 'Brezina', 'Cheguig', 'Chellala', 'El Bayadh', 'El Biodh Sidi Cheikh', 'El Bnoud', 'El Kheither', 'El Mehara', 'Ghassoul', 'Kef El Ahmar', 'Krakda', 'Rogassa', 'Sidi Ameur', 'Sidi Slimane', 'Sidi Tifour', 'Stitten', 'Tousmouline'],
    'Illizi': ['Bordj Omar Driss', 'Debdeb', 'Illizi', 'In Amenas'],
    'Bordj Bou Arreridj': ['Ain Taghrout', 'Ain Tesra', 'Belimour', 'Ben Daoud', 'Bir Kasdali', 'Bordj Bou Arreridj', 'Bordj Ghdir', 'Bordj Zemora', 'Colla', 'Djaafra', 'El Ach', 'El Achir', 'El Anseur', 'El Hamadia', 'El Mhir', 'El Main', 'Ghilassa', 'Haraza', 'Hasnaoua', 'Khelil', 'Ksour', 'Mansoura', 'Medjana', 'Ouled Brahem', 'Ouled Dahmane', 'Ouled Sidi Brahim', 'Rabta', 'Ras El Oued', 'Sidi Embarek', 'Tafreg', 'Taglait', 'Teniet En Nasr', 'Tesmart', 'Tixter'],
    'Boumerdès': ['Afir', 'Ammal', 'Baghlia', 'Ben Choud', 'Beni Amrane', 'Bordj Menaiel', 'Boudouaou', 'Boudouaou El Bahri', 'Boumerdes', 'Bouzegza Keddara', 'Chabet El Ameur', 'Corso', 'Dellys', 'Djinet', 'El Kharrouba', 'Hammedi', 'Isser', 'Khemis El Khechna', 'Larbatache', 'Leghata', 'Naciria', 'Ouled Aissa', 'Ouled Hedadj', 'Ouled Moussa', 'Si Mustapha', 'Sidi Daoud', 'Souk El Haad', 'Taourga', 'Thenia', 'Tidjelabine', 'Timezrit', 'Zemmouri'],   
     'El Tarf': ['Ain El Assel', 'Ain Kerma', 'Asfour', 'Ben M Hidi', 'Berrihane', 'Besbes', 'Bougous', 'Bouhadjar', 'Bouteldja', 'Chebaita Mokhtar', 'Chefia', 'Chihani', 'Drean', 'Echatt', 'El Aioun', 'El Kala', 'El Tarf', 'Hammam Beni Salah', 'Lac Des Oiseaux', 'Oued Zitoun', 'Raml Souk', 'Souarekh', 'Zerizer', 'Zitouna'],
    'Tindouf': ['Oum El Assel', 'Tindouf'],
    'Tissemsilt': ['Ammari', 'Beni Chaib', 'Beni Lahcene', 'Bordj Bounaama', 'Bordj El Emir Abdelkader', 'Bou Caid', 'Khemisti', 'Larbaa', 'Lardjem', 'Layoune', 'Lazharia', 'Maacem', 'Melaab', 'Ouled Bessem', 'Sidi Abed', 'Sidi Boutouchent', 'Sidi Lantri', 'Sidi Slimane', 'Tamellalet', 'Theniet El Had', 'Tissemsilt', 'Youssoufia'],
    'El Oued': ['Bayadha', 'Ben Guecha', 'Debila', 'Douar El Maa', 'El Ogla', 'El Oued', 'Guemar', 'Hamraia', 'Hassani Abdelkrim', 'Hassi Khalifa', 'Kouinine', 'Magrane', 'Mih Ouansa', 'Nakhla', 'Oued El Alenda', 'Ourmes', 'Reguiba', 'Robbah', 'Sidi Aoun', 'Taghzout', 'Taleb Larbi', 'Trifaoui'],
    'Khenchela': ['Ain Touila', 'Babar', 'Baghai', 'Bouhmama', 'Chelia', 'Cherchar', 'Djellal', 'El Hamma', 'El Mahmal', 'El Oueldja', 'Ensigha', 'Kais', 'Khenchela', 'Khirane', 'Msara', 'Mtoussa', 'Ouled Rechache', 'Remila', 'Tamza', 'Taouzianat', 'Yabous'],
    'Souk Ahras': ['Ain Soltane', 'Ain Zana', 'Bir Bouhouche', 'Drea', 'Haddada', 'Hanencha', 'Khedara', 'Khemissa', 'Mdaourouche', 'Machroha', 'Merahna', 'Oued Kebrit', 'Ouled Driss', 'Ouled Moumen', 'Oum El Adhaim', 'Quillen', 'Ragouba', 'Safel El Ouiden', 'Sedrata', 'Sidi Fredj', 'Souk Ahras', 'Taoura', 'Terraguelt', 'Tiffech', 'Zaarouria', 'Zouabi'],
    'Tipaza': ['Aghbal', 'Ahmer El Ain', 'Ain Tagourait', 'Attatba', 'Beni Mileuk', 'Bou Haroun', 'Bou Ismail', 'Bourkika', 'Chaiba', 'Cherchell', 'Damous', 'Douaouda', 'Fouka', 'Gouraya', 'Hadjout', 'Hadjret Ennous', 'Khemisti', 'Kolea', 'Larhat', 'Menaceur', 'Merad', 'Messelmoun', 'Nador', 'Sidi Amar', 'Sidi Ghiles', 'Sidi Rached', 'Sidi Semiane', 'Tipaza'],
    'Mila': ['Ahmed Rachedi', 'Ain Beida Harriche', 'Ain Mellouk', 'Ain Tine', 'Amira Arres', 'Benyahia Abderrahmane', 'Bouhatem', 'Chelghoum Laid', 'Chigara', 'Derrahi Bousselah', 'El Mechira', 'Elayadi Barbes', 'Ferdjioua', 'Grarem Gouga', 'Hamala', 'Mila', 'Minar Zarza', 'Oued Athmenia', 'Oued Endja', 'Oued Seguen', 'Ouled Khalouf', 'Rouached', 'Sidi Khelifa', 'Sidi Merouane', 'Tadjenanet', 'Tassadane Haddada', 'Teleghma', 'Terrai Bainem', 'Tessala', 'Tiberguent', 'Yahia Beniguecha', 'Zeghaia'],
    'Aïn Defla': ['Ain Benian', 'Ain Bouyahia', 'Ain Defla', 'Ain Lechiakh', 'Ain Soltane', 'Ain Tork', 'Arib', 'Barbouche', 'Bathia', 'Belaas', 'Ben Allal', 'Bir Ould Khelifa', 'Bordj Emir Khaled', 'Boumedfaa', 'Bourached', 'Djelida', 'Djemaa Ouled Cheikh', 'Djendel', 'El Abadia', 'El Amra', 'El Attaf', 'El Maine', 'Hammam Righa', 'Hassania', 'Hoceinia', 'Khemis Miliana', 'Mekhatria', 'Miliana', 'Oued Chorfa', 'Oued Djemaa', 'Rouina', 'Sidi Lakhdar', 'Tacheta Zegagha', 'Tarik Ibn Ziad', 'Tiberkanine', 'Zeddine'],
    'Naâma': ['Ain Ben Khelil', 'Ain Safra', 'Assela', 'Djeniane Bourzeg', 'El Biod', 'Kasdir', 'Makman Ben Amer', 'Mecheria', 'Moghrar', 'Naama', 'Sfissifa', 'Tiout'],
    'Aïn Témouchent': ['Aghlal', 'Ain El Arbaa', 'Ain Kihal', 'Ain Temouchent', 'Ain Tolba', 'Aoubellil', 'Beni Saf', 'Bouzedjar', 'Chaabat El Ham', 'Chentouf', 'El Amria', 'El Malah', 'El Messaid', 'Emir Abdelkader', 'Hammam Bouhadjar', 'Hassasna', 'Hassi El Ghella', 'Oued Berkeche', 'Oued Sebbah', 'Ouled Boudjemaa', 'Ouled Kihal', 'Oulhaca El Gheraba', 'Sidi Ben Adda', 'Sidi Boumediene', 'Sidi Ouriache', 'Sidi Safi', 'Tamzoura', 'Terga'],
    'Ghardaïa': ['Berriane', 'Bounoura', 'Dhayet Bendhahoua', 'El Atteuf', 'El Guerrara', 'Ghardaia', 'Mansoura', 'Metlili', 'Sebseb', 'Zelfana'],
    'Relizane': ['Ain Rahma', 'Ain Tarek', 'Ammi Moussa', 'Belaassel Bouzagza', 'Bendaoud', 'Beni Dergoun', 'Beni Zentis', 'Dar Ben Abdelah', 'Djidiouia', 'El Guettar', 'El Hmadna', 'El Hassi', 'El Matmar', 'El Ouldja', 'Had Echkalla', 'Hamri', 'Kalaa', 'Lahlef', 'Mazouna', 'Mediouna', 'Mendes', 'Merdja Sidi Abed', 'Ouarizane', 'Oued El Djemaa', 'Oued Essalem', 'Oued Rhiou', 'Ouled Aiche', 'Ouled Sidi Mihoub', 'Ramka', 'Relizane', 'Sidi Khettab', 'Sidi Lazreg', 'Sidi Mhamed Benali', 'Sidi Mhamed Benaouda', 'Sidi Saada', 'Souk El Had', 'Yellel', 'Zemmoura'],
    'Timimoun': ['Aougrout', 'Charouine', 'Deldoul', 'Ksar Kaddour', 'Metarfa', 'Ouled Aissa', 'Ouled Said', 'Talmine', 'Timimoun', 'Tinerkouk'],
    'Bordj Badji Mokhtar': ['Bordj Badji Mokhtar', 'Timiaouine'],
    'Ouled Djellal': ['Besbes', 'Chaiba', 'Doucen', 'Ouled Djellal', 'Ras El Miad', 'Sidi Khaled'],
    'Béni Abbès': ['Beni Abbes', 'Beni Ikhlef', 'El Ouata', 'Igli', 'Kerzaz', 'Ksabi', 'Ouled Khoudir', 'Tabelbala', 'Tamtert', 'Timoudi'],
    'In Salah': ['Foggaret Azzaouia', 'In Ghar', 'In Salah'],
    'In Guezzam': ['In Guezzam', 'Tin Zouatine'],
    'Touggourt': ['Benaceur', 'Blidet Amor', 'El Alia', 'El Hadjira', 'Megarine', 'Mnaguer', 'Nezla', 'Sidi Slimane', 'Taibet', 'Tebesbest', 'Temacine', 'Touggourt', 'Zaouia El Abidia'],
    'Djanet': ['Bordj El Haouasse', 'Djanet'],
    'El MGhair': ['Djamaa', 'El Mghair', 'Mrara', 'Oum Touyour', 'Sidi Amrane', 'Sidi Khelil', 'Still', 'Tenedla'],
    'El Meniaa': ['El Meniaa', 'Hassi Fehal', 'Hassi Gara']
    };

    // Create form elements
    const form = document.createElement('form');
    form.id = 'delivery-form';
    
    // Create hidden fields for delivery details
    const deliveryAddressField = document.createElement('input');
    deliveryAddressField.type = 'hidden';
    deliveryAddressField.id = 'delivery-address';
    deliveryAddressField.name = 'delivery_address';
    
    const deliveryPriceField = document.createElement('input');
    deliveryPriceField.type = 'hidden';
    deliveryPriceField.id = 'delivery-price';
    deliveryPriceField.name = 'delivery_price';
    
    // Add hidden fields to form
    form.appendChild(deliveryAddressField);
    form.appendChild(deliveryPriceField);

    // Find or create the form elements
    const wilayaField = document.querySelector('select[name="extra_fields[custom_field_RHwHakc5cRSN1W4j]"]') || 
                        createSelect('extra_fields[custom_field_RHwHakc5cRSN1W4j]', 'wilaya-select');
    
    const communeField = document.querySelector('select[name="extra_fields[custom_field_MtcWLJO1uGmfFgV0]"]') || 
                         createSelect('extra_fields[custom_field_MtcWLJO1uGmfFgV0]', 'commune-select');
    
    const deliveryTypeField = document.querySelector('select[name="extra_fields[custom_field_jseeWqwCUI6eEABf]"]') || 
                              createSelect('extra_fields[custom_field_jseeWqwCUI6eEABf]', 'delivery-type-select');
    
    // Create price display element
    const priceDisplay = document.createElement('div');
    priceDisplay.id = 'delivery-price-display';
    priceDisplay.className = 'form-text';
    priceDisplay.style.fontWeight = 'bold';
    priceDisplay.style.marginTop = '10px';
    priceDisplay.style.color = '#007bff';
    priceDisplay.style.fontSize = '14px';
    priceDisplay.style.padding = '5px';
    priceDisplay.style.backgroundColor = '#f8f9fa';
    priceDisplay.style.border = '1px solid #dee2e6';
    priceDisplay.style.borderRadius = '4px';
    priceDisplay.textContent = 'Sélectionnez un type de livraison pour voir le prix';
    


    // Clear existing options
    wilayaField.innerHTML = '';
    communeField.innerHTML = '';
    deliveryTypeField.innerHTML = '';

    // Add default option for wilaya
    addOption(wilayaField, '', 'Sélectionnez une wilaya');
    
    // Add wilaya options
    Object.keys(wilayaData).forEach(wilaya => {
        addOption(wilayaField, wilaya, wilaya);
    });

    // Add delivery type options
    addOption(deliveryTypeField, '', 'Sélectionnez un type de livraison');
    addOption(deliveryTypeField, 'home', 'Livraison à domicile');
    addOption(deliveryTypeField, 'desk', 'Livraison stopdesk');

    // Insert price display element after delivery type field
    if (deliveryTypeField.parentNode) {
        deliveryTypeField.parentNode.insertBefore(priceDisplay, deliveryTypeField.nextSibling);
    } else {
        // Fallback: append to the form or body if parentNode is not available
        const targetContainer = document.querySelector('form') || document.body;
        targetContainer.appendChild(priceDisplay);
    }

    // Add event listeners
    wilayaField.addEventListener('change', updateCommuneOptions);
    deliveryTypeField.addEventListener('change', updateCommuneOptions);
    wilayaField.addEventListener('change', updatePriceAndLocationDisplay);
    deliveryTypeField.addEventListener('change', updatePriceAndLocationDisplay);
    communeField.addEventListener('change', updatePriceAndLocationDisplay);

    // Update price display when delivery type changes
    function updatePriceAndLocationDisplay() {
        const selectedWilaya = wilayaField.value;
        const selectedDeliveryType = deliveryTypeField.value;

        // Update price display
        const prices = deliveryPrices[selectedWilaya] || {};
        const price = prices[selectedDeliveryType] || 0;

        if (selectedDeliveryType === 'home') {
            priceDisplay.textContent = `Prix de livraison à domicile: ${price} DA`;
        } else if (selectedDeliveryType === 'desk') {
            priceDisplay.textContent = `Prix de livraison stopdesk: ${price} DA`;
        } else {
            priceDisplay.textContent = 'Sélectionnez un type de livraison pour voir le prix';
        }
    }

    // Update commune options when wilaya changes
    function updateCommuneOptions() {
        const selectedWilaya = wilayaField.value;
        const selectedDeliveryType = deliveryTypeField.value;
        communeField.innerHTML = ''; // Clear existing options
        addOption(communeField, '', 'Sélectionnez une commune');
        
        if (selectedDeliveryType === 'desk') {
            // Get stopdesk locations for the selected wilaya
            const stopdesks = stopDeskLocations[selectedWilaya] || {};
            console.log('Selected Wilaya:', selectedWilaya);
            console.log('Stopdesk locations:', stopdesks);
            
            // Add only communes that have stopdesk locations
            const communes = Object.keys(stopdesks);
            console.log('Available communes:', communes);
            
            communes.forEach(commune => {
                addOption(communeField, commune, commune);
            });
        } else if (selectedDeliveryType === 'home') {
            // Show all communes for home delivery
            const communes = wilayaData[selectedWilaya] || [];
            communes.forEach(commune => {
                addOption(communeField, commune, commune);
            });
        }
        
        // Reset commune selection and update displays
        communeField.value = '';
        updatePriceAndLocationDisplay();
    }

    // Helper function to add an option to a select element
    function addOption(select, value, text) {
        const option = document.createElement('option');
        option.value = value;
        option.textContent = text;
        select.appendChild(option);
    }

    // Add select element helper function
    function createSelect(name, id) {
        const select = document.createElement('select');
        select.name = name;
        select.id = id;
        select.style.marginTop = '10px';
        document.querySelector('form').appendChild(select);
        return select;
    }
});
</script>



